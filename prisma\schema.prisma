// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

// User model for customers and administrators
model User {
  id            String    @id @default(cuid())
  name          String
  email         String    @unique
  emailVerified DateTime?
  password      String    // Hashed password
  address       String?
  contactNumber String?
  role          Role      @default(CUSTOMER)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  orders   Order[]
  accounts Account[]
  sessions Session[]

  @@map("users")
}

// Role enumeration
enum Role {
  CUSTOMER
  ADMIN
}

// Product model for grocery items
model Product {
  id        Int      @id @default(autoincrement())
  name      String
  price     Float
  quantity  Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  orderItems OrderItem[]

  @@map("products")
}

// Order model for customer orders
model Order {
  id          Int      @id @default(autoincrement())
  orderDate   DateTime @default(now())
  totalAmount Float
  status      String   @default("COMPLETED")

  // Foreign key
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Relations
  items OrderItem[]

  @@map("orders")
}

// OrderItem model for items within an order
model OrderItem {
  id       Int     @id @default(autoincrement())
  quantity Int
  price    Float   // Price at the time of purchase

  // Foreign keys
  orderId   Int
  order     Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  productId Int
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("order_items")
}

// NextAuth.js required models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}
